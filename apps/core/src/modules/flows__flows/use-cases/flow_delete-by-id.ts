import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { DatabaseService, Flow } from '@core/database';
import { NumericId } from '@libs/common/database';
import { RedisLock } from '@libs/redis';

@Injectable()
export class DeleteFlowByIdUseCase implements UseCase {
  private readonly logger = new Logger(DeleteFlowByIdUseCase.name);

  constructor(private readonly db: DatabaseService) {}

  @RedisLock(_args => 'project-x--flows')
  async execute({ flowId: _flowId }: { flowId: string }): Promise<Flow> {
    const flowId = NumericId(_flowId);

    this.logger.verbose({ msg: 'Started deleting flow', data: { flowId } });

    const [deletedFlow, nextFlow] = await this.db.flows.manager.transaction(async entityManager => {
      this.logger.verbose({ msg: 'Finding flow to delete', data: { flowId } });

      // Find flow to delete
      const flowToDelete = await entityManager.findOne(Flow, {
        where: { id: flowId },
      });

      if (!flowToDelete) throw new NotFoundException('Flow not found', 'FLOW_NOT_FOUND');

      this.logger.verbose({ msg: 'Flow found', data: flowToDelete });

      this.logger.verbose({ msg: 'Deleting flow', data: { flowId: flowToDelete.id } });

      // Delete flow
      await entityManager.delete(Flow, flowToDelete.id);

      this.logger.verbose({ msg: 'Flow deleted', data: { flowId: flowToDelete.id } });

      this.logger.verbose({
        msg: 'Finding next flow to update prevId',
        data: { prevId: flowToDelete.id },
      });

      // Update next flow prevId
      const nextFlow = await entityManager.findOne(Flow, {
        where: {
          prevId: flowToDelete.id,
        },
      });

      if (nextFlow) {
        this.logger.verbose({ msg: 'Next flow found', data: nextFlow });

        this.logger.verbose({
          msg: 'Updating next flow prevId',
          data: { prevId: flowToDelete.prevId },
        });

        nextFlow.prevId = flowToDelete.prevId;
        await entityManager.save(Flow, nextFlow);
      } else {
        this.logger.verbose({ msg: 'No next flow found' });
      }

      return [flowToDelete, nextFlow];
    });

    this.logger.log({
      msg: 'Flow deleted',
      data: {
        incomingData: { flowId },
        deletedFlow,
        modifiedData: { nextFlow },
      },
    });

    return deletedFlow;
  }
}
