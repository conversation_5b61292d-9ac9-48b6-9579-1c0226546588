import { RedisLockService } from './redis-lock.service';
import { getRedisLockService } from './redis.module.context';

export const RedisLock = <Args extends unknown[], Return>(resourceFactory: (...args: Args) => string) => {
  return (
    _target: object,
    propertyKey: string,
    descriptor: TypedPropertyDescriptor<(...args: Args) => Return>,
  ) => {
    const originalMethod = descriptor.value!;

    descriptor.value = function (...args: Args): Return {
      const mutex = getRedisLockService() as RedisLockService;

      // eslint-disable-next-line @typescript-eslint/no-unsafe-return
      const method = () => originalMethod.apply(this, args);

      const resource = resourceFactory?.(...args);
      if (!resource) return method() as Return;

      return mutex.useLock(method, resource) as Return;
    };

    return descriptor;
  };
};
